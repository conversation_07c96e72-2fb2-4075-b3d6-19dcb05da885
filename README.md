# Vanna AI Business Intelligence Dashboard

A comprehensive business intelligence solution using Vanna AI to query your SQL database with natural language and generate insights, statistics, and charts.

## Features

- 🤖 Natural language to SQL conversion using Vanna AI
- 📊 Interactive charts and visualizations
- 🗄️ Support for multiple database types (PostgreSQL, MySQL, SQLite, SQL Server)
- 🧠 Integration with free LLM models (Ollama, Hugging Face)
- 🎯 Business-specific language training
- 🌐 Web-based user interface
- 📈 Statistical analysis and insights

## Quick Start

### 1. Environment Setup

```bash
# Create virtual environment
python -m venv vanna-env
source vanna-env/bin/activate  # On Windows: vanna-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your database and LLM settings
nano .env
```

### 3. Run the Application

```bash
# Start the Streamlit web interface
streamlit run app.py

# Or run the Flask version
python flask_app.py
```

## Configuration

### Database Support

- **PostgreSQL**: `postgresql://user:pass@host:port/db`
- **MySQL**: `mysql+pymysql://user:pass@host:port/db`
- **SQLite**: `sqlite:///path/to/database.db`
- **SQL Server**: `mssql+pyodbc://user:pass@host:port/db`

### LLM Providers

- **Ollama** (Recommended for free usage)
- **Hugging Face Transformers**
- **OpenAI API** (requires API key)

## Project Structure

```
├── app.py              # Streamlit web interface
├── flask_app.py        # Flask web interface
├── config.py           # Configuration management
├── database.py         # Database connection utilities
├── llm_integration.py  # LLM provider integrations
├── vanna_setup.py      # Vanna AI configuration
├── requirements.txt    # Python dependencies
├── .env.example        # Environment variables template
└── README.md          # This file
```

## Usage

1. Configure your database connection in `.env`
2. Set up your preferred LLM provider
3. Run the training script to teach Vanna about your database
4. Start asking questions in natural language!

## Training Vanna

The system will automatically learn from:
- Your database schema
- Sample queries you provide
- Business-specific terminology
- Query patterns and results
