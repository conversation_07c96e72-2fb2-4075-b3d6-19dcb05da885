# Vanna AI Business Intelligence - Installation Guide

This guide will help you set up Vanna AI with a proper user interface to connect to your SQL database and ask questions to get insights, statistics, and charts using free LLM models.

## 🚀 Quick Start

### 1. Prerequisites

- Python 3.8 or higher
- Access to a SQL database (PostgreSQL, MySQL, SQLite, or SQL Server)
- Git (for cloning the repository)

### 2. Installation

```bash
# Clone or navigate to your project directory
cd /path/to/your/project

# Run the setup script
chmod +x setup.sh
./setup.sh
```

Or manually:

```bash
# Create virtual environment
python3 -m venv vanna-env
source vanna-env/bin/activate  # On Windows: vanna-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env  # or use your preferred editor
```

### 4. Database Setup

#### Option A: Use Your Existing Database
Edit `.env` with your database credentials:

```env
DB_TYPE=postgresql  # or mysql, sqlite, sqlserver
DB_HOST=your_host
DB_PORT=5432
DB_NAME=your_database
DB_USER=your_username
DB_PASSWORD=your_password
```

#### Option B: Create Sample Database (for testing)
```bash
python database_setup.py sample
```

### 5. LLM Setup (Free Options)

#### Option A: Ollama (Recommended - Completely Free)
```bash
# Install Ollama from https://ollama.ai/
# Pull a model
ollama pull llama2

# Update .env
LLM_PROVIDER=ollama
OLLAMA_MODEL=llama2
```

#### Option B: Hugging Face (Free with registration)
```bash
# Get free API token from https://huggingface.co/
# Update .env
LLM_PROVIDER=huggingface
HUGGINGFACE_API_TOKEN=your_token
```

### 6. Test Setup

```bash
# Quick test
python test_setup.py --quick

# Full test suite
python test_setup.py
```

### 7. Run the Application

#### Streamlit Interface (Recommended)
```bash
streamlit run app.py
```

#### Flask Interface
```bash
python flask_app.py
```

## 📊 Usage Guide

### 1. First Time Setup

1. **Connect to Database**: The system will automatically detect your database connection
2. **Train Vanna**: Go to "Train Vanna AI" tab and click "Train on Database Schema"
3. **Ask Questions**: Navigate to "Ask Questions" tab and start querying your data

### 2. Example Questions

Once trained, you can ask questions like:

- "What are the top 10 customers by total sales?"
- "Show me monthly revenue trends for the last year"
- "Which products have the highest profit margins?"
- "What is the average order value by customer segment?"
- "How many new customers did we acquire each month?"

### 3. Business Training

To improve results for your specific business:

1. Go to "Train Vanna AI" tab
2. Add business documentation in the "Business Documentation Training" section
3. Include:
   - Business terminology
   - Metric definitions
   - Domain-specific knowledge
   - Common business questions

## 🔧 Advanced Configuration

### Database Connection Strings

#### PostgreSQL
```
postgresql://username:password@host:port/database
```

#### MySQL
```
mysql+pymysql://username:password@host:port/database
```

#### SQLite
```
sqlite:///path/to/database.db
```

#### SQL Server
```
mssql+pyodbc://username:password@host:port/database?driver=ODBC+Driver+17+for+SQL+Server
```

### LLM Provider Configuration

#### Ollama Setup
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Available models
ollama pull llama2        # General purpose
ollama pull codellama     # Better for SQL
ollama pull mistral       # Fast and efficient

# Update .env
LLM_PROVIDER=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=codellama
```

#### Hugging Face Setup
```bash
# Install transformers
pip install transformers torch

# Update .env
LLM_PROVIDER=huggingface
HUGGINGFACE_API_TOKEN=your_token_here
```

### Custom Training

#### Automated Training Pipeline
```bash
python -c "from training_pipeline import training_pipeline; training_pipeline.run_complete_training()"
```

#### Manual Training
```python
from vanna_setup import vanna_manager

# Train on specific SQL queries
vanna_manager.vn.train(
    question="What are our top customers?",
    sql="SELECT customer_name, SUM(order_total) FROM orders GROUP BY customer_name ORDER BY SUM(order_total) DESC LIMIT 10"
)

# Train on documentation
vanna_manager.train_on_documentation("Customer Lifetime Value (CLV) is the total revenue we expect from a customer over their entire relationship with us.")
```

## 🐛 Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Test database connection
python database_setup.py test

# Check connection string format
python -c "from config import config; print(config.database.get_connection_string())"
```

#### LLM Provider Not Available
```bash
# Check Ollama status
curl http://localhost:11434/api/tags

# Test LLM providers
python -c "from llm_integration import llm_manager; print(llm_manager.get_provider_status())"
```

#### Vanna Training Issues
```bash
# Check Vanna status
python -c "from vanna_setup import vanna_manager; print(vanna_manager.is_ready())"

# Run training pipeline
python -c "from training_pipeline import training_pipeline; print(training_pipeline.run_complete_training())"
```

### Performance Optimization

#### For Large Databases
- Limit training to essential tables
- Use sample data for training
- Implement query result caching

#### For Better Query Generation
- Provide more business context in training
- Use domain-specific terminology
- Train on successful query patterns

## 📁 Project Structure

```
├── app.py                 # Streamlit web interface
├── flask_app.py          # Flask web interface  
├── config.py             # Configuration management
├── database.py           # Database utilities
├── llm_integration.py    # LLM provider integrations
├── vanna_setup.py        # Vanna AI configuration
├── training_pipeline.py  # Training automation
├── database_setup.py     # Database setup utilities
├── test_setup.py         # Testing suite
├── requirements.txt      # Dependencies
├── .env.example          # Environment template
└── README.md            # Documentation
```

## 🆘 Support

If you encounter issues:

1. Run the test suite: `python test_setup.py`
2. Check the logs for error messages
3. Verify your configuration in `.env`
4. Ensure your database is accessible
5. Confirm your LLM provider is running

## 🔄 Updates and Maintenance

### Regular Maintenance
- Retrain Vanna periodically with new data
- Update business documentation as terminology changes
- Monitor query performance and accuracy

### Updating Dependencies
```bash
pip install --upgrade -r requirements.txt
```
