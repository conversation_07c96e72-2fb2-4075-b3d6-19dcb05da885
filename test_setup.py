"""Comprehensive testing script for Vanna AI setup."""

import logging
import sys
import time
from typing import Dict, Any, List

# Import our modules
from database import db_manager
from llm_integration import llm_manager
from vanna_setup import vanna_manager
from training_pipeline import training_pipeline
from config import config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class VannaTestSuite:
    """Comprehensive test suite for Vanna AI setup."""
    
    def __init__(self):
        self.test_results = {}
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        print("🧪 Starting Vanna AI Test Suite")
        print("=" * 50)
        
        tests = [
            ("Configuration Test", self.test_configuration),
            ("Database Connection Test", self.test_database_connection),
            ("LLM Provider Test", self.test_llm_providers),
            ("Vanna Initialization Test", self.test_vanna_initialization),
            ("Database Schema Analysis Test", self.test_database_analysis),
            ("Training Pipeline Test", self.test_training_pipeline),
            ("Query Generation Test", self.test_query_generation),
            ("End-to-End Integration Test", self.test_integration)
        ]
        
        overall_success = True
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running {test_name}...")
            try:
                result = test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    overall_success = False
                    
            except Exception as e:
                error_msg = f"Test execution failed: {e}"
                print(f"💥 {test_name}: ERROR - {error_msg}")
                self.test_results[test_name] = {'success': False, 'error': error_msg}
                overall_success = False
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results.values() if result['success'])
        total = len(self.test_results)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Overall Status: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
        
        if not overall_success:
            print("\n🔧 Failed Tests:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"  - {test_name}: {result.get('error', 'Unknown error')}")
        
        return {
            'overall_success': overall_success,
            'passed': passed,
            'total': total,
            'results': self.test_results
        }
    
    def test_configuration(self) -> Dict[str, Any]:
        """Test configuration loading."""
        try:
            # Test database config
            db_config = config.database
            if not db_config.type or not db_config.name:
                return {'success': False, 'error': 'Database configuration incomplete'}
            
            # Test LLM config
            llm_config = config.llm
            if not llm_config.provider:
                return {'success': False, 'error': 'LLM provider not configured'}
            
            return {
                'success': True,
                'database_type': db_config.type,
                'llm_provider': llm_config.provider
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_database_connection(self) -> Dict[str, Any]:
        """Test database connectivity."""
        try:
            success = db_manager.connect()
            if not success:
                return {'success': False, 'error': 'Database connection failed'}
            
            # Test basic query
            tables = db_manager.get_table_names()
            
            return {
                'success': True,
                'table_count': len(tables),
                'tables': tables[:5]  # First 5 tables
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_llm_providers(self) -> Dict[str, Any]:
        """Test LLM provider availability."""
        try:
            provider_status = llm_manager.get_provider_status()
            active_providers = [name for name, status in provider_status.items() if status]
            
            if not active_providers:
                return {'success': False, 'error': 'No LLM providers available'}
            
            # Test generation
            test_prompt = "Hello, this is a test."
            response = llm_manager.generate_response(test_prompt)
            
            return {
                'success': True,
                'active_providers': active_providers,
                'test_response_length': len(response) if response else 0
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_vanna_initialization(self) -> Dict[str, Any]:
        """Test Vanna AI initialization."""
        try:
            if not vanna_manager.is_ready():
                return {'success': False, 'error': 'Vanna manager not ready'}
            
            return {
                'success': True,
                'model_name': vanna_manager.model_name
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_database_analysis(self) -> Dict[str, Any]:
        """Test database schema analysis."""
        try:
            if not db_manager.connect():
                return {'success': False, 'error': 'Database connection required'}
            
            db_summary = db_manager.get_database_summary()
            
            if not db_summary['tables']:
                return {'success': False, 'error': 'No tables found in database'}
            
            # Test sample data retrieval
            first_table = db_summary['tables'][0]['name']
            sample_data = db_manager.get_sample_data(first_table, 3)
            
            return {
                'success': True,
                'total_tables': db_summary['total_tables'],
                'sample_data_rows': len(sample_data)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_training_pipeline(self) -> Dict[str, Any]:
        """Test training pipeline functionality."""
        try:
            if not vanna_manager.is_ready():
                return {'success': False, 'error': 'Vanna not ready for training'}
            
            if not db_manager.connect():
                return {'success': False, 'error': 'Database connection required for training'}
            
            # Test database schema training
            schema_success = training_pipeline.train_database_schema()
            if not schema_success:
                return {'success': False, 'error': 'Database schema training failed'}
            
            return {
                'success': True,
                'schema_training': schema_success
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_query_generation(self) -> Dict[str, Any]:
        """Test SQL query generation."""
        try:
            if not vanna_manager.is_ready():
                return {'success': False, 'error': 'Vanna not ready'}
            
            # Test simple query generation
            test_questions = [
                "How many records are in the first table?",
                "Show me some sample data",
                "What tables are available?"
            ]
            
            successful_queries = 0
            
            for question in test_questions:
                try:
                    result = vanna_manager.ask_question(question)
                    if result.get('success') and result.get('sql'):
                        successful_queries += 1
                except:
                    continue
            
            if successful_queries == 0:
                return {'success': False, 'error': 'No queries generated successfully'}
            
            return {
                'success': True,
                'successful_queries': successful_queries,
                'total_test_queries': len(test_questions)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_integration(self) -> Dict[str, Any]:
        """Test end-to-end integration."""
        try:
            # Test complete workflow
            if not db_manager.connect():
                return {'success': False, 'error': 'Database connection failed'}
            
            if not vanna_manager.is_ready():
                return {'success': False, 'error': 'Vanna not ready'}
            
            # Get table info
            tables = db_manager.get_table_names()
            if not tables:
                return {'success': False, 'error': 'No tables available'}
            
            # Test query with actual table
            first_table = tables[0]
            question = f"How many records are in {first_table}?"
            
            result = vanna_manager.ask_question(question)
            
            if not result.get('success'):
                return {'success': False, 'error': f"Integration test query failed: {result.get('error')}"}
            
            return {
                'success': True,
                'test_table': first_table,
                'query_result_rows': len(result.get('data', []))
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


def main():
    """Main test execution function."""
    if len(sys.argv) > 1 and sys.argv[1] == '--quick':
        # Quick test mode
        print("🚀 Running Quick Tests...")
        test_suite = VannaTestSuite()
        
        quick_tests = [
            ("Configuration Test", test_suite.test_configuration),
            ("Database Connection Test", test_suite.test_database_connection),
            ("LLM Provider Test", test_suite.test_llm_providers)
        ]
        
        for test_name, test_func in quick_tests:
            print(f"\n🔍 {test_name}...")
            result = test_func()
            status = "✅ PASSED" if result['success'] else f"❌ FAILED: {result.get('error')}"
            print(f"   {status}")
    else:
        # Full test suite
        test_suite = VannaTestSuite()
        results = test_suite.run_all_tests()
        
        # Exit with appropriate code
        sys.exit(0 if results['overall_success'] else 1)


if __name__ == "__main__":
    main()
