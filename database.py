"""Database connection and utilities for Vanna AI application."""

import logging
from typing import Optional, List, Dict, Any
import pandas as pd
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.engine import Engine
from config import config

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self):
        self.engine: Optional[Engine] = None
        self.connection_string = config.database.get_connection_string()
    
    def connect(self) -> bool:
        """Establish database connection."""
        try:
            self.engine = create_engine(self.connection_string)
            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection established successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def get_engine(self) -> Engine:
        """Get database engine, connecting if necessary."""
        if self.engine is None:
            if not self.connect():
                raise ConnectionError("Could not establish database connection")
        return self.engine
    
    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute SQL query and return results as DataFrame."""
        try:
            engine = self.get_engine()
            df = pd.read_sql_query(query, engine)
            logger.info(f"Query executed successfully, returned {len(df)} rows")
            return df
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def get_table_names(self) -> List[str]:
        """Get list of all table names in the database."""
        try:
            engine = self.get_engine()
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            logger.info(f"Found {len(tables)} tables in database")
            return tables
        except Exception as e:
            logger.error(f"Failed to get table names: {e}")
            return []
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get schema information for a specific table."""
        try:
            engine = self.get_engine()
            inspector = inspect(engine)
            
            columns = inspector.get_columns(table_name)
            primary_keys = inspector.get_pk_constraint(table_name)
            foreign_keys = inspector.get_foreign_keys(table_name)
            indexes = inspector.get_indexes(table_name)
            
            schema = {
                'table_name': table_name,
                'columns': columns,
                'primary_keys': primary_keys,
                'foreign_keys': foreign_keys,
                'indexes': indexes
            }
            
            logger.info(f"Retrieved schema for table: {table_name}")
            return schema
        except Exception as e:
            logger.error(f"Failed to get schema for table {table_name}: {e}")
            return {}
    
    def get_sample_data(self, table_name: str, limit: int = 5) -> pd.DataFrame:
        """Get sample data from a table."""
        try:
            query = f"SELECT * FROM {table_name} LIMIT {limit}"
            return self.execute_query(query)
        except Exception as e:
            logger.error(f"Failed to get sample data from {table_name}: {e}")
            return pd.DataFrame()
    
    def get_database_summary(self) -> Dict[str, Any]:
        """Get comprehensive database summary."""
        summary = {
            'connection_string': self.connection_string.split('@')[0] + '@***',  # Hide credentials
            'tables': [],
            'total_tables': 0
        }
        
        try:
            table_names = self.get_table_names()
            summary['total_tables'] = len(table_names)
            
            for table_name in table_names:
                table_info = {
                    'name': table_name,
                    'schema': self.get_table_schema(table_name),
                    'sample_data': self.get_sample_data(table_name, 3)
                }
                summary['tables'].append(table_info)
            
            logger.info("Database summary generated successfully")
        except Exception as e:
            logger.error(f"Failed to generate database summary: {e}")
        
        return summary


# Global database manager instance
db_manager = DatabaseManager()
