"""Configuration management for Vanna AI application."""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    
    type: str = Field(default="postgresql", env="DB_TYPE")
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    name: str = Field(default="", env="DB_NAME")
    user: str = Field(default="", env="DB_USER")
    password: str = Field(default="", env="DB_PASSWORD")
    url: Optional[str] = Field(default=None, env="DB_URL")
    
    def get_connection_string(self) -> str:
        """Generate database connection string."""
        if self.url:
            return self.url
        
        if self.type == "sqlite":
            return f"sqlite:///{self.name}"
        elif self.type == "postgresql":
            return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
        elif self.type == "mysql":
            return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
        elif self.type == "sqlserver":
            return f"mssql+pyodbc://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}?driver=ODBC+Driver+17+for+SQL+Server"
        else:
            raise ValueError(f"Unsupported database type: {self.type}")


class LLMConfig(BaseSettings):
    """LLM configuration settings."""
    
    provider: str = Field(default="ollama", env="LLM_PROVIDER")
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    huggingface_token: Optional[str] = Field(default=None, env="HUGGINGFACE_API_TOKEN")
    ollama_base_url: str = Field(default="http://localhost:11434", env="OLLAMA_BASE_URL")
    ollama_model: str = Field(default="llama2", env="OLLAMA_MODEL")


class VannaConfig(BaseSettings):
    """Vanna AI configuration settings."""
    
    model_name: str = Field(default="my_business_model", env="VANNA_MODEL_NAME")
    api_key: Optional[str] = Field(default=None, env="VANNA_API_KEY")


class AppConfig(BaseSettings):
    """Application configuration settings."""
    
    host: str = Field(default="localhost", env="APP_HOST")
    port: int = Field(default=8501, env="APP_PORT")
    debug: bool = Field(default=True, env="DEBUG")
    secret_key: str = Field(default="dev-secret-key", env="SECRET_KEY")


class Config:
    """Main configuration class."""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.llm = LLMConfig()
        self.vanna = VannaConfig()
        self.app = AppConfig()


# Global configuration instance
config = Config()
