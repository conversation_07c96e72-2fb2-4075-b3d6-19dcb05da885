"""Database setup and testing utilities."""

import logging
import sys
from typing import Dict, Any, List
from database import db_manager
from config import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_database_connection() -> bool:
    """Test database connection and display results."""
    print("🔍 Testing database connection...")
    print(f"Database type: {config.database.type}")
    print(f"Host: {config.database.host}")
    print(f"Port: {config.database.port}")
    print(f"Database: {config.database.name}")
    print()
    
    try:
        success = db_manager.connect()
        if success:
            print("✅ Database connection successful!")
            return True
        else:
            print("❌ Database connection failed!")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False


def display_database_info():
    """Display comprehensive database information."""
    if not db_manager.connect():
        print("❌ Cannot connect to database")
        return
    
    print("📊 Database Information:")
    print("=" * 50)
    
    # Get table names
    tables = db_manager.get_table_names()
    print(f"Total tables: {len(tables)}")
    print()
    
    # Display each table info
    for table_name in tables[:10]:  # Limit to first 10 tables
        print(f"📋 Table: {table_name}")
        print("-" * 30)
        
        # Get schema
        schema = db_manager.get_table_schema(table_name)
        columns = schema.get('columns', [])
        
        print(f"Columns ({len(columns)}):")
        for col in columns[:5]:  # Show first 5 columns
            col_type = str(col['type'])
            nullable = "NULL" if col.get('nullable', True) else "NOT NULL"
            print(f"  - {col['name']}: {col_type} ({nullable})")
        
        if len(columns) > 5:
            print(f"  ... and {len(columns) - 5} more columns")
        
        # Get sample data
        sample_data = db_manager.get_sample_data(table_name, 3)
        if not sample_data.empty:
            print(f"\nSample data ({len(sample_data)} rows):")
            print(sample_data.to_string(index=False, max_cols=5))
        
        print("\n")
    
    if len(tables) > 10:
        print(f"... and {len(tables) - 10} more tables")


def setup_sample_database():
    """Create a sample database for testing (SQLite)."""
    print("🏗️ Setting up sample database...")
    
    # Only works with SQLite for demo purposes
    if config.database.type != 'sqlite':
        print("Sample database setup only available for SQLite")
        return
    
    try:
        import sqlite3
        import pandas as pd
        
        # Create sample data
        customers_data = {
            'customer_id': [1, 2, 3, 4, 5],
            'name': ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Charlie Wilson'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'city': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
            'signup_date': ['2023-01-15', '2023-02-20', '2023-03-10', '2023-04-05', '2023-05-12']
        }
        
        orders_data = {
            'order_id': [101, 102, 103, 104, 105, 106, 107, 108],
            'customer_id': [1, 2, 1, 3, 4, 2, 5, 3],
            'product': ['Laptop', 'Phone', 'Tablet', 'Headphones', 'Monitor', 'Keyboard', 'Mouse', 'Speaker'],
            'amount': [1200.00, 800.00, 400.00, 150.00, 300.00, 80.00, 25.00, 120.00],
            'order_date': ['2023-06-01', '2023-06-02', '2023-06-03', '2023-06-04', '2023-06-05', '2023-06-06', '2023-06-07', '2023-06-08']
        }
        
        # Create database
        conn = sqlite3.connect(config.database.name)
        
        # Create tables
        customers_df = pd.DataFrame(customers_data)
        orders_df = pd.DataFrame(orders_data)
        
        customers_df.to_sql('customers', conn, if_exists='replace', index=False)
        orders_df.to_sql('orders', conn, if_exists='replace', index=False)
        
        conn.close()
        
        print("✅ Sample database created successfully!")
        print("Tables created: customers, orders")
        print("You can now test the application with sample data")
        
    except Exception as e:
        print(f"❌ Failed to create sample database: {e}")


def interactive_setup():
    """Interactive database setup wizard."""
    print("🧙‍♂️ Database Setup Wizard")
    print("=" * 30)
    
    # Test current configuration
    print("1. Testing current configuration...")
    if test_database_connection():
        print("✅ Current configuration works!")
        
        choice = input("\nWould you like to see database information? (y/n): ").lower()
        if choice == 'y':
            display_database_info()
        return
    
    print("\n2. Current configuration failed. Let's set up a sample database.")
    choice = input("Create a sample SQLite database for testing? (y/n): ").lower()
    
    if choice == 'y':
        # Update config for SQLite
        config.database.type = 'sqlite'
        config.database.name = 'sample_business.db'
        
        setup_sample_database()
        
        # Test again
        print("\n3. Testing sample database...")
        if test_database_connection():
            print("✅ Sample database setup successful!")
            display_database_info()
        else:
            print("❌ Sample database setup failed")
    else:
        print("\nPlease update your .env file with correct database credentials:")
        print("- DB_TYPE (postgresql, mysql, sqlite, sqlserver)")
        print("- DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD")
        print("Or set DB_URL with full connection string")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "test":
            test_database_connection()
        elif command == "info":
            display_database_info()
        elif command == "sample":
            setup_sample_database()
        else:
            print("Usage: python database_setup.py [test|info|sample]")
    else:
        interactive_setup()
