"""Training pipeline for Vanna AI with business-specific customization."""

import logging
import json
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd

from database import db_manager
from vanna_setup import vanna_manager
from config import config

logger = logging.getLogger(__name__)


class TrainingPipeline:
    """Comprehensive training pipeline for Vanna AI."""
    
    def __init__(self):
        self.training_data_dir = "training_data"
        self.ensure_training_directory()
    
    def ensure_training_directory(self):
        """Create training data directory if it doesn't exist."""
        if not os.path.exists(self.training_data_dir):
            os.makedirs(self.training_data_dir)
    
    def run_complete_training(self) -> Dict[str, Any]:
        """Run complete training pipeline."""
        results = {
            'timestamp': datetime.now().isoformat(),
            'steps': {},
            'success': True,
            'errors': []
        }
        
        logger.info("Starting complete training pipeline...")
        
        # Step 1: Database schema training
        try:
            logger.info("Step 1: Training on database schema...")
            schema_success = self.train_database_schema()
            results['steps']['database_schema'] = {
                'success': schema_success,
                'timestamp': datetime.now().isoformat()
            }
            if not schema_success:
                results['success'] = False
                results['errors'].append("Database schema training failed")
        except Exception as e:
            logger.error(f"Database schema training error: {e}")
            results['steps']['database_schema'] = {'success': False, 'error': str(e)}
            results['success'] = False
            results['errors'].append(f"Database schema training error: {e}")
        
        # Step 2: Sample queries training
        try:
            logger.info("Step 2: Training on sample queries...")
            queries_success = self.train_sample_queries()
            results['steps']['sample_queries'] = {
                'success': queries_success,
                'timestamp': datetime.now().isoformat()
            }
            if not queries_success:
                results['success'] = False
                results['errors'].append("Sample queries training failed")
        except Exception as e:
            logger.error(f"Sample queries training error: {e}")
            results['steps']['sample_queries'] = {'success': False, 'error': str(e)}
            results['success'] = False
            results['errors'].append(f"Sample queries training error: {e}")
        
        # Step 3: Business terminology training
        try:
            logger.info("Step 3: Training on business terminology...")
            terminology_success = self.train_business_terminology()
            results['steps']['business_terminology'] = {
                'success': terminology_success,
                'timestamp': datetime.now().isoformat()
            }
            if not terminology_success:
                results['success'] = False
                results['errors'].append("Business terminology training failed")
        except Exception as e:
            logger.error(f"Business terminology training error: {e}")
            results['steps']['business_terminology'] = {'success': False, 'error': str(e)}
            results['success'] = False
            results['errors'].append(f"Business terminology training error: {e}")
        
        # Step 4: Save training metadata
        self.save_training_metadata(results)
        
        logger.info(f"Training pipeline completed. Success: {results['success']}")
        return results
    
    def train_database_schema(self) -> bool:
        """Train on database schema with enhanced metadata."""
        if not vanna_manager.is_ready():
            logger.error("Vanna manager not ready")
            return False
        
        if not db_manager.connect():
            logger.error("Database connection failed")
            return False
        
        try:
            # Get comprehensive database information
            db_summary = db_manager.get_database_summary()
            
            # Train on each table with detailed information
            for table_info in db_summary['tables']:
                table_name = table_info['name']
                schema = table_info['schema']
                
                # Create enhanced DDL with comments
                ddl = self._create_enhanced_ddl(table_name, schema)
                vanna_manager.vn.train(ddl=ddl)
                
                # Add table description
                table_description = self._generate_table_description(table_name, schema)
                vanna_manager.vn.train(documentation=table_description)
                
                logger.info(f"Trained on table: {table_name}")
            
            # Save schema information
            schema_file = os.path.join(self.training_data_dir, "database_schema.json")
            with open(schema_file, 'w') as f:
                json.dump(db_summary, f, indent=2, default=str)
            
            return True
            
        except Exception as e:
            logger.error(f"Database schema training failed: {e}")
            return False
    
    def train_sample_queries(self) -> bool:
        """Train on comprehensive sample queries."""
        if not vanna_manager.is_ready():
            return False
        
        try:
            # Load or generate sample queries
            sample_queries = self._generate_comprehensive_sample_queries()
            
            # Train on each query
            for query_info in sample_queries:
                sql = query_info['sql']
                description = query_info['description']
                
                # Train with SQL and description
                vanna_manager.vn.train(
                    question=description,
                    sql=sql
                )
                
                logger.info(f"Trained on query: {description[:50]}...")
            
            # Save sample queries
            queries_file = os.path.join(self.training_data_dir, "sample_queries.json")
            with open(queries_file, 'w') as f:
                json.dump(sample_queries, f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Sample queries training failed: {e}")
            return False
    
    def train_business_terminology(self) -> bool:
        """Train on business-specific terminology and concepts."""
        if not vanna_manager.is_ready():
            return False
        
        try:
            # Generate business terminology documentation
            business_docs = self._generate_business_documentation()
            
            # Train on business documentation
            for doc in business_docs:
                vanna_manager.vn.train(documentation=doc)
                logger.info(f"Trained on business documentation: {doc[:50]}...")
            
            # Save business documentation
            docs_file = os.path.join(self.training_data_dir, "business_terminology.json")
            with open(docs_file, 'w') as f:
                json.dump(business_docs, f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Business terminology training failed: {e}")
            return False
    
    def _create_enhanced_ddl(self, table_name: str, schema: Dict[str, Any]) -> str:
        """Create enhanced DDL with business context."""
        columns = schema.get('columns', [])
        
        ddl_parts = [f"-- Table: {table_name}"]
        ddl_parts.append(f"-- Business Purpose: {self._infer_table_purpose(table_name)}")
        ddl_parts.append(f"CREATE TABLE {table_name} (")
        
        column_definitions = []
        for column in columns:
            col_name = column['name']
            col_type = str(column['type'])
            nullable = "" if column.get('nullable', True) else " NOT NULL"
            
            # Add business context comment
            business_meaning = self._infer_column_meaning(col_name, col_type)
            comment = f" -- {business_meaning}" if business_meaning else ""
            
            column_definitions.append(f"  {col_name} {col_type}{nullable}{comment}")
        
        ddl_parts.append(",\n".join(column_definitions))
        ddl_parts.append(");")
        
        return "\n".join(ddl_parts)
    
    def _generate_table_description(self, table_name: str, schema: Dict[str, Any]) -> str:
        """Generate business description for a table."""
        purpose = self._infer_table_purpose(table_name)
        columns = schema.get('columns', [])
        
        description = f"""
        Table: {table_name}
        Purpose: {purpose}
        
        Key columns:
        """
        
        for column in columns[:5]:  # Top 5 columns
            col_meaning = self._infer_column_meaning(column['name'], str(column['type']))
            description += f"- {column['name']}: {col_meaning}\n"
        
        return description
    
    def _infer_table_purpose(self, table_name: str) -> str:
        """Infer business purpose of a table from its name."""
        name_lower = table_name.lower()
        
        if 'customer' in name_lower or 'client' in name_lower:
            return "Stores customer/client information and demographics"
        elif 'order' in name_lower or 'purchase' in name_lower:
            return "Records customer orders and purchase transactions"
        elif 'product' in name_lower or 'item' in name_lower:
            return "Contains product catalog and inventory information"
        elif 'employee' in name_lower or 'staff' in name_lower:
            return "Manages employee information and HR data"
        elif 'sale' in name_lower or 'transaction' in name_lower:
            return "Tracks sales transactions and revenue data"
        elif 'invoice' in name_lower or 'billing' in name_lower:
            return "Handles billing and invoice management"
        elif 'inventory' in name_lower or 'stock' in name_lower:
            return "Manages inventory levels and stock tracking"
        else:
            return f"Business data table for {table_name.replace('_', ' ').title()}"
    
    def _infer_column_meaning(self, col_name: str, col_type: str) -> str:
        """Infer business meaning of a column."""
        name_lower = col_name.lower()
        type_lower = col_type.lower()
        
        # ID columns
        if name_lower.endswith('_id') or name_lower == 'id':
            return "Unique identifier"
        
        # Date/time columns
        if 'date' in name_lower or 'time' in name_lower or 'timestamp' in name_lower:
            return "Date/time information"
        
        # Amount/price columns
        if 'amount' in name_lower or 'price' in name_lower or 'cost' in name_lower:
            return "Monetary value"
        
        # Name columns
        if 'name' in name_lower or 'title' in name_lower:
            return "Descriptive name or title"
        
        # Email/contact columns
        if 'email' in name_lower:
            return "Email address"
        elif 'phone' in name_lower:
            return "Phone number"
        elif 'address' in name_lower:
            return "Physical address"
        
        # Status columns
        if 'status' in name_lower or 'state' in name_lower:
            return "Current status or state"
        
        # Quantity columns
        if 'quantity' in name_lower or 'qty' in name_lower or 'count' in name_lower:
            return "Quantity or count"
        
        return f"Business attribute: {col_name.replace('_', ' ').title()}"

    def _generate_comprehensive_sample_queries(self) -> List[Dict[str, str]]:
        """Generate comprehensive sample queries for training."""
        if not db_manager.connect():
            return []

        queries = []
        tables = db_manager.get_table_names()

        for table_name in tables:
            schema = db_manager.get_table_schema(table_name)
            columns = schema.get('columns', [])

            # Basic queries
            queries.extend([
                {
                    'sql': f"SELECT * FROM {table_name} LIMIT 10",
                    'description': f"Show me the first 10 records from {table_name}"
                },
                {
                    'sql': f"SELECT COUNT(*) FROM {table_name}",
                    'description': f"How many records are in {table_name}?"
                }
            ])

            # Column-specific queries
            for column in columns[:3]:
                col_name = column['name']
                col_type = str(column['type']).lower()

                if 'int' in col_type or 'numeric' in col_type or 'decimal' in col_type:
                    queries.extend([
                        {
                            'sql': f"SELECT AVG({col_name}) FROM {table_name}",
                            'description': f"What is the average {col_name} in {table_name}?"
                        },
                        {
                            'sql': f"SELECT MAX({col_name}), MIN({col_name}) FROM {table_name}",
                            'description': f"What are the maximum and minimum {col_name} values?"
                        }
                    ])
                elif 'varchar' in col_type or 'text' in col_type:
                    queries.append({
                        'sql': f"SELECT DISTINCT {col_name} FROM {table_name} LIMIT 10",
                        'description': f"What are the different {col_name} values?"
                    })

        return queries

    def _generate_business_documentation(self) -> List[str]:
        """Generate business-specific documentation."""
        docs = [
            """
            Business Metrics and KPIs:
            - Revenue: Total income from sales
            - Customer Acquisition Cost (CAC): Cost to acquire a new customer
            - Customer Lifetime Value (CLV): Total value a customer brings over their lifetime
            - Monthly Recurring Revenue (MRR): Predictable monthly revenue
            - Churn Rate: Percentage of customers who stop using the service
            - Average Order Value (AOV): Average amount spent per order
            """,
            """
            Common Business Terms:
            - Lead: Potential customer who has shown interest
            - Conversion: When a lead becomes a paying customer
            - Retention: Keeping existing customers engaged
            - Upselling: Selling additional products to existing customers
            - Cross-selling: Selling complementary products
            - Funnel: The customer journey from awareness to purchase
            """,
            """
            Financial Terms:
            - Gross Revenue: Total income before expenses
            - Net Revenue: Income after deducting costs
            - Profit Margin: Percentage of revenue that is profit
            - EBITDA: Earnings before interest, taxes, depreciation, and amortization
            - Cash Flow: Money moving in and out of the business
            - ROI: Return on Investment
            """,
            """
            Sales and Marketing Terms:
            - Lead Generation: Process of attracting potential customers
            - Qualified Lead: Lead that meets specific criteria
            - Pipeline: Collection of potential deals in various stages
            - Close Rate: Percentage of leads that become customers
            - Attribution: Crediting marketing channels for conversions
            - Segmentation: Dividing customers into groups based on characteristics
            """
        ]

        return docs

    def save_training_metadata(self, results: Dict[str, Any]):
        """Save training metadata and results."""
        metadata_file = os.path.join(self.training_data_dir, "training_metadata.json")

        # Load existing metadata if it exists
        existing_metadata = []
        if os.path.exists(metadata_file):
            try:
                with open(metadata_file, 'r') as f:
                    existing_metadata = json.load(f)
            except:
                existing_metadata = []

        # Add new training session
        existing_metadata.append(results)

        # Save updated metadata
        with open(metadata_file, 'w') as f:
            json.dump(existing_metadata, f, indent=2)

        logger.info(f"Training metadata saved to {metadata_file}")

    def get_training_history(self) -> List[Dict[str, Any]]:
        """Get training history."""
        metadata_file = os.path.join(self.training_data_dir, "training_metadata.json")

        if not os.path.exists(metadata_file):
            return []

        try:
            with open(metadata_file, 'r') as f:
                return json.load(f)
        except:
            return []


# Global training pipeline instance
training_pipeline = TrainingPipeline()
