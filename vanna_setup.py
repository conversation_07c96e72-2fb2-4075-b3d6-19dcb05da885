"""Vanna AI setup and configuration."""

import logging
from typing import List, Dict, Any, Optional
import vanna
from vanna.local import LocalContext_OpenAI
from vanna.chromadb import ChromaDB_VectorStore
from database import db_manager
from llm_integration import llm_manager
from config import config

logger = logging.getLogger(__name__)


class VannaManager:
    """Manages Vanna AI configuration and training."""
    
    def __init__(self):
        self.vn = None
        self.model_name = config.vanna.model_name
        self._initialize_vanna()
    
    def _initialize_vanna(self):
        """Initialize Vanna with local LLM and vector store."""
        try:
            # Use ChromaDB for vector storage and local LLM
            class CustomVanna(ChromaDB_VectorStore, LocalContext_OpenAI):
                def __init__(self, config=None):
                    ChromaDB_VectorStore.__init__(self, config=config)
                    LocalContext_OpenAI.__init__(self, config=config)
                
                def generate_sql(self, question: str, **kwargs) -> str:
                    """Override to use our LLM manager."""
                    # Get relevant context from vector store
                    context = self.get_related_ddl(question)
                    
                    # Create prompt for SQL generation
                    prompt = f"""
                    Given the following database schema and question, generate a SQL query.
                    
                    Database Schema:
                    {context}
                    
                    Question: {question}
                    
                    Generate only the SQL query without any explanation:
                    """
                    
                    # Use our LLM manager
                    response = llm_manager.generate_response(prompt, **kwargs)
                    return response.strip()
            
            # Initialize Vanna with custom configuration
            vanna_config = {
                'model': self.model_name,
                'path': f'./vanna_models/{self.model_name}'
            }
            
            self.vn = CustomVanna(config=vanna_config)
            logger.info("Vanna initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Vanna: {e}")
            self.vn = None
    
    def is_ready(self) -> bool:
        """Check if Vanna is ready to use."""
        return self.vn is not None
    
    def train_on_database(self) -> bool:
        """Train Vanna on the connected database schema."""
        if not self.is_ready():
            logger.error("Vanna not initialized")
            return False
        
        try:
            # Connect to database
            if not db_manager.connect():
                logger.error("Cannot connect to database for training")
                return False
            
            # Get database summary
            db_summary = db_manager.get_database_summary()
            
            # Train on each table
            for table_info in db_summary['tables']:
                table_name = table_info['name']
                schema = table_info['schema']
                
                # Create DDL statement for training
                ddl = self._create_ddl_from_schema(table_name, schema)
                self.vn.train(ddl=ddl)
                
                # Add sample queries if available
                sample_queries = self._generate_sample_queries(table_name, schema)
                for query in sample_queries:
                    self.vn.train(sql=query)
                
                logger.info(f"Trained on table: {table_name}")
            
            logger.info("Database training completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database training failed: {e}")
            return False
    
    def _create_ddl_from_schema(self, table_name: str, schema: Dict[str, Any]) -> str:
        """Create DDL statement from table schema."""
        columns = schema.get('columns', [])
        
        ddl_parts = [f"CREATE TABLE {table_name} ("]
        
        column_definitions = []
        for column in columns:
            col_name = column['name']
            col_type = str(column['type'])
            nullable = "" if column.get('nullable', True) else " NOT NULL"
            column_definitions.append(f"  {col_name} {col_type}{nullable}")
        
        ddl_parts.append(",\n".join(column_definitions))
        ddl_parts.append(");")
        
        return "\n".join(ddl_parts)
    
    def _generate_sample_queries(self, table_name: str, schema: Dict[str, Any]) -> List[str]:
        """Generate sample queries for a table."""
        queries = []
        
        # Basic SELECT query
        queries.append(f"SELECT * FROM {table_name} LIMIT 10;")
        
        # Count query
        queries.append(f"SELECT COUNT(*) FROM {table_name};")
        
        # Add more specific queries based on column types
        columns = schema.get('columns', [])
        for column in columns[:3]:  # Limit to first 3 columns
            col_name = column['name']
            col_type = str(column['type']).lower()
            
            if 'int' in col_type or 'numeric' in col_type or 'decimal' in col_type:
                queries.append(f"SELECT AVG({col_name}) FROM {table_name};")
                queries.append(f"SELECT MAX({col_name}) FROM {table_name};")
            elif 'varchar' in col_type or 'text' in col_type or 'char' in col_type:
                queries.append(f"SELECT DISTINCT {col_name} FROM {table_name} LIMIT 10;")
        
        return queries
    
    def train_on_documentation(self, documentation: str):
        """Train Vanna on business documentation."""
        if not self.is_ready():
            logger.error("Vanna not initialized")
            return False
        
        try:
            self.vn.train(documentation=documentation)
            logger.info("Documentation training completed")
            return True
        except Exception as e:
            logger.error(f"Documentation training failed: {e}")
            return False
    
    def ask_question(self, question: str) -> Dict[str, Any]:
        """Ask a question and get SQL + results."""
        if not self.is_ready():
            return {"error": "Vanna not initialized"}
        
        try:
            # Generate SQL
            sql = self.vn.generate_sql(question)
            
            if not sql:
                return {"error": "Could not generate SQL for the question"}
            
            # Execute SQL
            df = db_manager.execute_query(sql)
            
            # Generate explanation
            explanation = self._generate_explanation(question, sql, df)
            
            return {
                "question": question,
                "sql": sql,
                "data": df,
                "explanation": explanation,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Question processing failed: {e}")
            return {"error": str(e)}
    
    def _generate_explanation(self, question: str, sql: str, df) -> str:
        """Generate explanation for the query results."""
        prompt = f"""
        Explain the following SQL query and its results in business terms:
        
        Question: {question}
        SQL Query: {sql}
        Number of results: {len(df)}
        
        Provide a brief, business-friendly explanation:
        """
        
        return llm_manager.generate_response(prompt, max_tokens=200)


# Global Vanna manager instance
vanna_manager = VannaManager()
