"""LLM integration for Vanna AI application."""

import logging
from typing import Optional, Dict, Any
import requests
from config import config

logger = logging.getLogger(__name__)


class LLMProvider:
    """Base class for LLM providers."""
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from LLM."""
        raise NotImplementedError
    
    def is_available(self) -> bool:
        """Check if LLM provider is available."""
        raise NotImplementedError


class OllamaProvider(LLMProvider):
    """Ollama LLM provider for local models."""
    
    def __init__(self):
        self.base_url = config.llm.ollama_base_url
        self.model = config.llm.ollama_model
    
    def is_available(self) -> bool:
        """Check if Ollama is running and model is available."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                available_models = [model['name'] for model in models]
                return any(self.model in model for model in available_models)
            return False
        except Exception as e:
            logger.error(f"Ollama availability check failed: {e}")
            return False
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using Ollama."""
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                **kwargs
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                logger.error(f"Ollama API error: {response.status_code}")
                return ""
        except Exception as e:
            logger.error(f"Ollama generation failed: {e}")
            return ""


class HuggingFaceProvider(LLMProvider):
    """Hugging Face Transformers provider."""
    
    def __init__(self):
        self.token = config.llm.huggingface_token
        self.model_name = "microsoft/DialoGPT-medium"  # Default model
    
    def is_available(self) -> bool:
        """Check if Hugging Face is available."""
        try:
            import transformers
            return True
        except ImportError:
            logger.error("Transformers library not installed")
            return False
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using Hugging Face Transformers."""
        try:
            from transformers import pipeline
            
            generator = pipeline(
                "text-generation",
                model=self.model_name,
                token=self.token
            )
            
            result = generator(
                prompt,
                max_length=kwargs.get('max_length', 200),
                num_return_sequences=1,
                temperature=kwargs.get('temperature', 0.7)
            )
            
            return result[0]['generated_text']
        except Exception as e:
            logger.error(f"Hugging Face generation failed: {e}")
            return ""


class OpenAIProvider(LLMProvider):
    """OpenAI API provider."""
    
    def __init__(self):
        self.api_key = config.llm.openai_api_key
        self.model = "gpt-3.5-turbo"
    
    def is_available(self) -> bool:
        """Check if OpenAI API is available."""
        return self.api_key is not None and len(self.api_key) > 0
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using OpenAI API."""
        try:
            import openai
            
            client = openai.OpenAI(api_key=self.api_key)
            
            response = client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get('max_tokens', 500),
                temperature=kwargs.get('temperature', 0.7)
            )
            
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI generation failed: {e}")
            return ""


class LLMManager:
    """Manages LLM providers and routing."""
    
    def __init__(self):
        self.providers = {
            'ollama': OllamaProvider(),
            'huggingface': HuggingFaceProvider(),
            'openai': OpenAIProvider()
        }
        self.current_provider = None
        self._initialize_provider()
    
    def _initialize_provider(self):
        """Initialize the configured LLM provider."""
        provider_name = config.llm.provider.lower()
        
        if provider_name in self.providers:
            provider = self.providers[provider_name]
            if provider.is_available():
                self.current_provider = provider
                logger.info(f"Initialized LLM provider: {provider_name}")
            else:
                logger.warning(f"Configured provider {provider_name} is not available")
        
        # Fallback to first available provider
        if self.current_provider is None:
            for name, provider in self.providers.items():
                if provider.is_available():
                    self.current_provider = provider
                    logger.info(f"Using fallback LLM provider: {name}")
                    break
        
        if self.current_provider is None:
            logger.error("No LLM providers are available")
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using current provider."""
        if self.current_provider is None:
            return "No LLM provider available"
        
        return self.current_provider.generate_response(prompt, **kwargs)
    
    def get_provider_status(self) -> Dict[str, bool]:
        """Get status of all providers."""
        return {name: provider.is_available() for name, provider in self.providers.items()}


# Global LLM manager instance
llm_manager = LLMManager()
