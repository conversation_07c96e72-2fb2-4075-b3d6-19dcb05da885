#!/bin/bash

# Vanna AI Business Intelligence Setup Script

echo "🚀 Setting up Vanna AI Business Intelligence Dashboard..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv vanna-env

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source vanna-env/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️ Creating .env file from template..."
    cp .env.example .env
    echo "✏️ Please edit .env file with your database and LLM configuration"
fi

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your configuration"
echo "2. Activate the virtual environment: source vanna-env/bin/activate"
echo "3. Run the application: streamlit run app.py"
echo ""
echo "For Ollama setup (free LLM):"
echo "1. Install Ollama: https://ollama.ai/"
echo "2. Pull a model: ollama pull llama2"
echo "3. Set LLM_PROVIDER=ollama in .env"
