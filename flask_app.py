"""Flask web interface for Vanna AI Business Intelligence Dashboard."""

from flask import Flask, render_template, request, jsonify, session
import json
from datetime import datetime
import logging

# Import our modules
from database import db_manager
from llm_integration import llm_manager
from vanna_setup import vanna_manager
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = config.app.secret_key

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """Get system status."""
    db_status = db_manager.connect()
    llm_status = llm_manager.get_provider_status()
    vanna_status = vanna_manager.is_ready()
    
    return jsonify({
        'database': db_status,
        'llm': llm_status,
        'vanna': vanna_status,
        'active_llm_providers': [name for name, status in llm_status.items() if status]
    })

@app.route('/api/database/overview')
def database_overview():
    """Get database overview."""
    if not db_manager.connect():
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        db_summary = db_manager.get_database_summary()
        return jsonify(db_summary)
    except Exception as e:
        logger.error(f"Database overview error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/query', methods=['POST'])
def process_query():
    """Process natural language query."""
    data = request.get_json()
    question = data.get('question', '').strip()
    
    if not question:
        return jsonify({'error': 'Question is required'}), 400
    
    if not db_manager.connect():
        return jsonify({'error': 'Database connection failed'}), 500
    
    if not vanna_manager.is_ready():
        return jsonify({'error': 'Vanna AI is not ready'}), 500
    
    try:
        result = vanna_manager.ask_question(question)
        
        if result.get('success'):
            # Convert DataFrame to JSON
            df = result['data']
            result['data'] = df.to_dict('records')
            result['columns'] = df.columns.tolist()
            result['row_count'] = len(df)
            
            # Store in session history
            if 'query_history' not in session:
                session['query_history'] = []
            
            session['query_history'].append({
                'timestamp': datetime.now().isoformat(),
                'question': question,
                'sql': result['sql'],
                'rows': len(df)
            })
            
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Query processing error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/train/database', methods=['POST'])
def train_database():
    """Train Vanna on database schema."""
    if not db_manager.connect():
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        success = vanna_manager.train_on_database()
        if success:
            return jsonify({'success': True, 'message': 'Database training completed'})
        else:
            return jsonify({'error': 'Database training failed'}), 500
    except Exception as e:
        logger.error(f"Database training error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/train/documentation', methods=['POST'])
def train_documentation():
    """Train Vanna on business documentation."""
    data = request.get_json()
    documentation = data.get('documentation', '').strip()
    
    if not documentation:
        return jsonify({'error': 'Documentation is required'}), 400
    
    try:
        success = vanna_manager.train_on_documentation(documentation)
        if success:
            return jsonify({'success': True, 'message': 'Documentation training completed'})
        else:
            return jsonify({'error': 'Documentation training failed'}), 500
    except Exception as e:
        logger.error(f"Documentation training error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/history')
def get_history():
    """Get query history."""
    history = session.get('query_history', [])
    return jsonify(history)

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(
        host=config.app.host,
        port=config.app.port,
        debug=config.app.debug
    )
