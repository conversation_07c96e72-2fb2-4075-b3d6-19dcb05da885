# Database Configuration
DB_TYPE=postgresql  # postgresql, mysql, sqlite, sqlserver
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password
DB_URL=postgresql://username:password@localhost:5432/database_name

# LLM Configuration
LLM_PROVIDER=ollama  # ollama, openai, huggingface, local
OPENAI_API_KEY=your_openai_api_key_if_using_openai
HUGGINGFACE_API_TOKEN=your_hf_token_if_using_hf
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2  # or codellama, mistral, etc.

# Vanna Configuration
VANNA_MODEL_NAME=my_business_model
VANNA_API_KEY=your_vanna_api_key_if_using_cloud

# Application Configuration
APP_HOST=localhost
APP_PORT=8501
DEBUG=True
SECRET_KEY=your_secret_key_for_flask_sessions
