"""Streamlit web interface for Vanna AI Business Intelligence Dashboard."""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import logging

# Import our modules
from database import db_manager
from llm_integration import llm_manager
from vanna_setup import vanna_manager
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Vanna AI Business Intelligence",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .status-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if 'db_connected' not in st.session_state:
        st.session_state.db_connected = False
    if 'vanna_trained' not in st.session_state:
        st.session_state.vanna_trained = False
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []


def check_system_status():
    """Check and display system component status."""
    st.sidebar.header("🔧 System Status")
    
    # Database status
    db_status = db_manager.connect()
    if db_status:
        st.sidebar.success("✅ Database Connected")
        st.session_state.db_connected = True
    else:
        st.sidebar.error("❌ Database Disconnected")
        st.session_state.db_connected = False
    
    # LLM status
    llm_status = llm_manager.get_provider_status()
    active_providers = [name for name, status in llm_status.items() if status]
    
    if active_providers:
        st.sidebar.success(f"✅ LLM Ready ({', '.join(active_providers)})")
    else:
        st.sidebar.error("❌ No LLM Available")
    
    # Vanna status
    if vanna_manager.is_ready():
        st.sidebar.success("✅ Vanna AI Ready")
    else:
        st.sidebar.error("❌ Vanna AI Not Ready")


def database_overview():
    """Display database overview and statistics."""
    st.header("📊 Database Overview")
    
    if not st.session_state.db_connected:
        st.error("Please connect to your database first.")
        return
    
    try:
        # Get database summary
        db_summary = db_manager.get_database_summary()
        
        # Display metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Tables", db_summary['total_tables'])
        
        with col2:
            total_columns = sum(len(table['schema'].get('columns', [])) for table in db_summary['tables'])
            st.metric("Total Columns", total_columns)
        
        with col3:
            st.metric("Database Type", config.database.type.upper())
        
        # Display table information
        st.subheader("📋 Tables")
        
        for table_info in db_summary['tables']:
            with st.expander(f"Table: {table_info['name']}"):
                schema = table_info['schema']
                columns = schema.get('columns', [])
                
                # Column information
                st.write("**Columns:**")
                col_df = pd.DataFrame([
                    {
                        'Column': col['name'],
                        'Type': str(col['type']),
                        'Nullable': 'Yes' if col.get('nullable', True) else 'No'
                    }
                    for col in columns
                ])
                st.dataframe(col_df, use_container_width=True)
                
                # Sample data
                sample_data = table_info['sample_data']
                if not sample_data.empty:
                    st.write("**Sample Data:**")
                    st.dataframe(sample_data, use_container_width=True)
    
    except Exception as e:
        st.error(f"Error loading database overview: {e}")


def natural_language_query():
    """Natural language query interface."""
    st.header("🤖 Ask Questions About Your Data")

    if not st.session_state.db_connected:
        st.error("Please connect to your database first.")
        return

    if not vanna_manager.is_ready():
        st.error("Vanna AI is not ready. Please check your configuration.")
        return

    # Query input
    question = st.text_area(
        "Ask a question about your data:",
        placeholder="e.g., What are the top 5 customers by total order amount?",
        height=100
    )

    # Example questions
    with st.expander("💡 Example Questions"):
        st.write("""
        - What are the top 10 customers by total sales?
        - Show me monthly revenue trends for the last year
        - Which products have the highest profit margins?
        - What is the average order value by customer segment?
        - How many new customers did we acquire each month?
        """)

    if st.button("🔍 Ask Question", type="primary"):
        if question.strip():
            with st.spinner("Processing your question..."):
                result = vanna_manager.ask_question(question)

                if result.get('success'):
                    # Display SQL query
                    st.subheader("📝 Generated SQL")
                    st.code(result['sql'], language='sql')

                    # Display results
                    st.subheader("📊 Results")
                    df = result['data']

                    if not df.empty:
                        st.dataframe(df, use_container_width=True)

                        # Auto-generate charts for numeric data
                        create_visualizations(df, question)

                        # Display explanation
                        if result.get('explanation'):
                            st.subheader("💡 Explanation")
                            st.write(result['explanation'])

                        # Add to history
                        st.session_state.query_history.append({
                            'timestamp': datetime.now(),
                            'question': question,
                            'sql': result['sql'],
                            'rows': len(df)
                        })
                    else:
                        st.info("Query executed successfully but returned no results.")

                else:
                    st.error(f"Error: {result.get('error', 'Unknown error')}")
        else:
            st.warning("Please enter a question.")


def create_visualizations(df, question):
    """Create appropriate visualizations for the data."""
    numeric_columns = df.select_dtypes(include=['number']).columns
    categorical_columns = df.select_dtypes(include=['object', 'category']).columns

    if len(numeric_columns) > 0:
        st.subheader("📈 Visualization")

        # Choose chart type based on data characteristics
        if len(df) <= 20 and len(categorical_columns) > 0 and len(numeric_columns) > 0:
            # Bar chart for small datasets with categories
            fig = px.bar(
                df.head(10),
                x=categorical_columns[0],
                y=numeric_columns[0],
                title=question
            )
            st.plotly_chart(fig, use_container_width=True)

        elif len(numeric_columns) >= 2:
            # Scatter plot for multiple numeric columns
            fig = px.scatter(
                df,
                x=numeric_columns[0],
                y=numeric_columns[1],
                title=question
            )
            st.plotly_chart(fig, use_container_width=True)

        elif len(numeric_columns) == 1:
            # Histogram for single numeric column
            fig = px.histogram(
                df,
                x=numeric_columns[0],
                title=f"Distribution of {numeric_columns[0]}"
            )
            st.plotly_chart(fig, use_container_width=True)


def query_history():
    """Display query history."""
    st.header("📜 Query History")
    
    if not st.session_state.query_history:
        st.info("No queries executed yet.")
        return
    
    for i, query in enumerate(reversed(st.session_state.query_history)):
        with st.expander(f"Query {len(st.session_state.query_history) - i}: {query['question'][:50]}..."):
            st.write(f"**Timestamp:** {query['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
            st.write(f"**Question:** {query['question']}")
            st.write(f"**Rows Returned:** {query['rows']}")
            st.code(query['sql'], language='sql')


def training_interface():
    """Vanna training interface."""
    st.header("🎓 Train Vanna AI")
    
    if not st.session_state.db_connected:
        st.error("Please connect to your database first.")
        return
    
    # Auto-training on database schema
    st.subheader("🗄️ Database Schema Training")
    
    if st.button("🚀 Train on Database Schema", type="primary"):
        with st.spinner("Training Vanna on your database schema..."):
            success = vanna_manager.train_on_database()
            
            if success:
                st.success("✅ Database schema training completed!")
                st.session_state.vanna_trained = True
            else:
                st.error("❌ Database schema training failed.")
    
    # Business documentation training
    st.subheader("📚 Business Documentation Training")
    
    documentation = st.text_area(
        "Add business-specific documentation:",
        placeholder="Describe your business terms, metrics, and domain knowledge...",
        height=150
    )
    
    if st.button("📖 Train on Documentation"):
        if documentation.strip():
            with st.spinner("Training on documentation..."):
                success = vanna_manager.train_on_documentation(documentation)
                
                if success:
                    st.success("✅ Documentation training completed!")
                else:
                    st.error("❌ Documentation training failed.")
        else:
            st.warning("Please enter some documentation.")


def main():
    """Main application function."""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🤖 Vanna AI Business Intelligence Dashboard</h1>', unsafe_allow_html=True)
    
    # Sidebar
    check_system_status()
    
    # Navigation
    st.sidebar.header("📋 Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["Database Overview", "Ask Questions", "Query History", "Train Vanna AI"]
    )
    
    # Page routing
    if page == "Database Overview":
        database_overview()
    elif page == "Ask Questions":
        natural_language_query()
    elif page == "Query History":
        query_history()
    elif page == "Train Vanna AI":
        training_interface()


if __name__ == "__main__":
    main()
